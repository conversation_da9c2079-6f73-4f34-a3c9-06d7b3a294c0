/**
 * <PERSON><PERSON><PERSON> para testar as correções dos comandos ping e traceroute no MikrotikExecutor
 * 
 * Este script testa especificamente os problemas identificados nos logs:
 * 1. Formatação duplicada de comando
 * 2. Detecção de conclusão falhando
 * 3. Prompt do Mikrotik não detectado
 * 4. Timeout específico não sendo respeitado
 * 
 * Uso: node test-mikrotik-diagnostics-fixed.js <host> <porta> <usuário> <senha>
 */

// Importar dependências
const { NodeSSH } = require('node-ssh');

// Configurar logger simples
const Logger = {
  log: (...args) => console.log(`INFO [${new Date().toLocaleTimeString()}]:`, ...args),
  error: (...args) => console.error(`ERROR [${new Date().toLocaleTimeString()}]:`, ...args),
  warn: (...args) => console.warn(`WARN [${new Date().toLocaleTimeString()}]:`, ...args)
};

// Verificar argumentos
const args = process.argv.slice(2);
if (args.length < 4) {
  console.error('Uso: node test-mikrotik-diagnostics-fixed.js <host> <porta> <usuário> <senha>');
  process.exit(1);
}

const [host, port, username, password] = args;

// Comandos de teste focados nos problemas identificados
const testCommands = [
  {
    name: 'Ping simples - Teste formatação sem duplicação',
    command: 'ping *******',
    expectedFormat: '/ping address=******* count=4',
    expectedTimeout: 35000,
    description: 'Verifica se não há duplicação de "address=" na formatação'
  },
  {
    name: 'Ping com sintaxe Mikrotik - Teste preservação',
    command: '/ping address=******* count=1',
    expectedFormat: '/ping address=******* count=1',
    expectedTimeout: 35000,
    description: 'Verifica se comando já formatado não é alterado'
  },
  {
    name: 'Ping localhost - Teste detecção de prompt',
    command: '/ping address=127.0.0.1 count=2',
    expectedFormat: '/ping address=127.0.0.1 count=2',
    expectedTimeout: 35000,
    description: 'Testa detecção do prompt [user@hostname] >'
  }
];

// Função para testar formatação de comando
function testCommandFormatting() {
  Logger.log('\n=== TESTE DE FORMATAÇÃO DE COMANDOS ===');
  
  // Simular a função formatDiagnosticCommand
  function formatDiagnosticCommand(command) {
    const cleanCommand = command.trim().toLowerCase();
    
    // Se o comando já está na sintaxe correta do Mikrotik, retornar como está
    if (command.startsWith('/ping address=') || 
        command.startsWith('/tool traceroute address=') ||
        command.startsWith('/tool ping address=')) {
      return command.trim();
    }
    
    // Detectar comandos ping simples e converter para sintaxe Mikrotik
    if (cleanCommand.startsWith('ping ') || cleanCommand === 'ping') {
      const parts = command.trim().split(/\s+/);
      if (parts.length >= 2) {
        const target = parts[1];
        return `/ping address=${target} count=4`;
      }
      return '/ping';
    }
    
    // Para comandos que já começam com /, manter como estão
    if (command.startsWith('/')) {
      return command.trim();
    }
    
    return `/${command.trim()}`;
  }
  
  testCommands.forEach(test => {
    const formatted = formatDiagnosticCommand(test.command);
    const isCorrect = formatted === test.expectedFormat;
    
    Logger.log(`Comando: ${test.command}`);
    Logger.log(`Esperado: ${test.expectedFormat}`);
    Logger.log(`Resultado: ${formatted}`);
    Logger.log(`Status: ${isCorrect ? '✅ CORRETO' : '❌ INCORRETO'}`);
    Logger.log('---');
  });
}

// Função principal
async function main() {
  // Primeiro, testar a formatação localmente
  testCommandFormatting();
  
  const ssh = new NodeSSH();
  
  try {
    Logger.log(`\n=== CONECTANDO AO MIKROTIK ===`);
    Logger.log(`Host: ${host}:${port}`);
    Logger.log(`Usuário: ${username}`);
    
    // Conectar via SSH
    await ssh.connect({
      host: host,
      port: parseInt(port),
      username: username,
      password: password,
      readyTimeout: 30000,
      algorithms: {
        kex: ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
        cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
        hmac: ['hmac-sha2-256', 'hmac-sha1']
      }
    });
    
    Logger.log('✅ Conexão SSH estabelecida com sucesso!');
    
    // Importar o MikrotikExecutor
    const { MikrotikExecutor } = require('../src/services/ssh/executors/mikrotikExecutor');
    const executor = new MikrotikExecutor(ssh);
    
    Logger.log('\n=== INICIANDO TESTES REAIS ===');
    
    // Executar cada comando de teste
    for (let i = 0; i < testCommands.length; i++) {
      const test = testCommands[i];
      
      Logger.log(`\n--- TESTE ${i + 1}/${testCommands.length}: ${test.name} ---`);
      Logger.log(`Descrição: ${test.description}`);
      Logger.log(`Comando: ${test.command}`);
      Logger.log(`Timeout esperado: ${test.expectedTimeout}ms`);
      
      try {
        const startTime = Date.now();
        
        // Executar o comando
        Logger.log('🚀 Executando comando...');
        const result = await executor.executeCommand(test.command);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        Logger.log(`⏱️ Duração: ${duration}ms`);
        Logger.log(`📊 Código de saída: ${result.code}`);
        
        // Verificar se o timeout foi respeitado
        const timeoutOk = duration < test.expectedTimeout;
        Logger.log(`⏰ Timeout respeitado: ${timeoutOk ? '✅' : '❌'} (${duration}ms < ${test.expectedTimeout}ms)`);
        
        if (result.stdout) {
          Logger.log('📤 Saída (primeiras 200 chars):');
          console.log(result.stdout.substring(0, 200) + (result.stdout.length > 200 ? '...' : ''));
        }
        
        if (result.stderr) {
          Logger.warn('⚠️ Erros:');
          console.log(result.stderr);
        }
        
        // Verificar se o comando foi bem-sucedido
        if (result.code === 0 && timeoutOk) {
          Logger.log('✅ TESTE PASSOU - Comando executado corretamente');
        } else {
          Logger.error('❌ TESTE FALHOU - Problemas detectados');
        }
        
      } catch (error) {
        Logger.error(`❌ TESTE FALHOU - Erro: ${error.message}`);
      }
      
      // Aguardar entre testes
      if (i < testCommands.length - 1) {
        Logger.log('⏳ Aguardando 3 segundos...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }
    
    Logger.log('\n=== RESUMO DOS TESTES ===');
    Logger.log('✅ Testes de correção concluídos');
    Logger.log('📋 Verifique os logs acima para identificar problemas restantes');
    
  } catch (error) {
    Logger.error('💥 Erro durante os testes:', error);
    process.exit(1);
  } finally {
    // Limpar recursos
    try {
      await ssh.dispose();
      Logger.log('🔌 Conexão SSH encerrada');
    } catch (error) {
      Logger.error('⚠️ Erro ao encerrar SSH:', error);
    }
  }
}

// Executar o script
main().catch(error => {
  Logger.error('💥 Erro fatal:', error);
  process.exit(1);
});
