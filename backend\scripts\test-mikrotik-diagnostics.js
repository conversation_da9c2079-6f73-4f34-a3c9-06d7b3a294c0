/**
 * Script para testar comandos de diagnóstico (ping e traceroute) em dispositivos Mikrotik
 * 
 * Este script testa especificamente os comandos ping e traceroute que foram corrigidos
 * no MikrotikExecutor para garantir que funcionem adequadamente.
 * 
 * Uso: node test-mikrotik-diagnostics.js <host> <porta> <usuário> <senha>
 */

// Importar dependências
const { NodeSSH } = require('node-ssh');
const path = require('path');

// Configurar logger simples
const Logger = {
  log: (...args) => console.log(`INFO [${new Date().toLocaleTimeString()}]:`, ...args),
  error: (...args) => console.error(`ERROR [${new Date().toLocaleTimeString()}]:`, ...args),
  warn: (...args) => console.warn(`WARN [${new Date().toLocaleTimeString()}]:`, ...args)
};

// Verificar argumentos
const args = process.argv.slice(2);
if (args.length < 4) {
  console.error('Uso: node test-mikrotik-diagnostics.js <host> <porta> <usuário> <senha>');
  process.exit(1);
}

const [host, port, username, password] = args;

// Comandos de teste para diagnóstico
const testCommands = [
  {
    name: 'Ping simples (sintaxe tradicional)',
    command: 'ping *******',
    expectedFormat: '/ping address=******* count=4'
  },
  {
    name: 'Ping com sintaxe Mikrotik',
    command: '/ping address=******* count=3',
    expectedFormat: '/ping address=******* count=3'
  },
  {
    name: 'Traceroute simples (sintaxe tradicional)',
    command: 'traceroute *******',
    expectedFormat: '/tool traceroute address=*******'
  },
  {
    name: 'Traceroute com sintaxe Mikrotik',
    command: '/tool traceroute address=*******',
    expectedFormat: '/tool traceroute address=*******'
  }
];

// Função principal
async function main() {
  const ssh = new NodeSSH();
  
  try {
    Logger.log(`Conectando ao Mikrotik ${host}:${port} como ${username}...`);
    
    // Conectar via SSH
    await ssh.connect({
      host: host,
      port: parseInt(port),
      username: username,
      password: password,
      readyTimeout: 30000,
      algorithms: {
        kex: ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
        cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
        hmac: ['hmac-sha2-256', 'hmac-sha1']
      }
    });
    
    Logger.log('Conexão SSH estabelecida com sucesso!');
    
    // Importar o MikrotikExecutor
    const { MikrotikExecutor } = require('../src/services/ssh/executors/mikrotikExecutor');
    const executor = new MikrotikExecutor(ssh);
    
    Logger.log('Iniciando testes de comandos de diagnóstico...\n');
    
    // Executar cada comando de teste
    for (let i = 0; i < testCommands.length; i++) {
      const test = testCommands[i];
      
      Logger.log(`\n=== TESTE ${i + 1}/${testCommands.length}: ${test.name} ===`);
      Logger.log(`Comando original: ${test.command}`);
      Logger.log(`Formato esperado: ${test.expectedFormat}`);
      
      try {
        const startTime = Date.now();
        
        // Executar o comando
        const result = await executor.executeCommand(test.command);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        Logger.log(`Comando executado em ${duration}ms`);
        Logger.log(`Código de saída: ${result.code}`);
        
        if (result.stdout) {
          Logger.log('Saída (stdout):');
          console.log(result.stdout);
        }
        
        if (result.stderr) {
          Logger.warn('Erros (stderr):');
          console.log(result.stderr);
        }
        
        // Verificar se o comando foi bem-sucedido
        if (result.code === 0) {
          Logger.log('✅ TESTE PASSOU - Comando executado com sucesso');
        } else {
          Logger.error('❌ TESTE FALHOU - Comando retornou código de erro');
        }
        
      } catch (error) {
        Logger.error(`❌ TESTE FALHOU - Erro ao executar comando: ${error.message}`);
      }
      
      // Aguardar um pouco entre os testes
      if (i < testCommands.length - 1) {
        Logger.log('Aguardando 5 segundos antes do próximo teste...');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    Logger.log('\n=== RESUMO DOS TESTES ===');
    Logger.log('Todos os testes de diagnóstico foram concluídos.');
    Logger.log('Verifique os resultados acima para identificar possíveis problemas.');
    
  } catch (error) {
    Logger.error('Erro durante os testes:', error);
    process.exit(1);
  } finally {
    // Limpar recursos
    try {
      await ssh.dispose();
      Logger.log('Conexão SSH encerrada.');
    } catch (error) {
      Logger.error('Erro ao encerrar conexão SSH:', error);
    }
  }
}

// Executar o script
main().catch(error => {
  Logger.error('Erro fatal:', error);
  process.exit(1);
});
