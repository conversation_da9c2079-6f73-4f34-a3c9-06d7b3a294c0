import { NodeSSH } from 'node-ssh';
import { RouterOSAPI } from 'node-routeros-v2';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { BaseExecutor } from './baseExecutor';

/**
 * Executor de comandos para dispositivos Mikrotik
 * Implementação otimizada que prioriza SSH com configurações robustas
 * e fallback para API nativa quando necessário
 */
export class MikrotikExecutor extends BaseExecutor {
  private routerOsApi: RouterOSAPI | null = null;
  private apiConnected: boolean = false;
  private host: string = '';
  private username: string = '';
  private password: string = '';
  private port: number = 8728; // Porta padrão da API Mikrotik
  private connectionAttempts: number = 0;
  private maxConnectionAttempts: number = 2; // Reduzido para 2 tentativas
  private keepaliveInterval: NodeJS.Timeout | null = null;
  private lastCommandTime: number = 0;

  constructor(ssh: NodeSSH) {
    super(ssh);

    // Configurar timeouts otimizados para Mikrotik
    this.BASE_TIMEOUT = 45000; // 45 segundos como base (reduzido)
    this.TIMEOUT_PER_COMMAND = 8000; // 8 segundos adicionais por comando (reduzido)
    this.MAX_TIMEOUT = 120000; // Limite máximo de 2 minutos (reduzido)

    // Extrair informações de conexão do SSH para usar na API
    if (ssh.connection) {
      const config = ssh.connection.config;
      this.host = config.host || '';
      this.username = config.username || '';
      this.password = config.password || '';

      // Inicializar keepalive manual para Mikrotik
      this.startKeepalive();
    }

    // Configurar handler para limpar recursos quando o SSH for encerrado
    ssh.connection?.on('close', () => {
      this.cleanup();
    });

    ssh.connection?.on('error', (error: any) => {
      Logger.error('Erro na conexão SSH Mikrotik:', error);
      this.cleanup();
    });
  }

  /**
   * Inicia o keepalive manual para manter a conexão SSH ativa
   */
  private startKeepalive(): void {
    if (this.keepaliveInterval) {
      clearInterval(this.keepaliveInterval);
    }

    Logger.log('Iniciando keepalive manual para Mikrotik a cada 30 segundos');

    this.keepaliveInterval = setInterval(async () => {
      try {
        // Verificar se passou tempo suficiente desde o último comando
        const timeSinceLastCommand = Date.now() - this.lastCommandTime;

        // Só enviar keepalive se não houve comando recente (últimos 25 segundos)
        if (timeSinceLastCommand > 25000) {
          // Enviar comando simples para manter a conexão ativa
          await this.ssh.execCommand('system identity print', {
            execOptions: { pty: false } // Sem PTY para keepalive
          });
          Logger.log('Keepalive enviado para Mikrotik');
        }
      } catch (error) {
        // Ignorar erros de keepalive silenciosamente
        // Logger.log('Keepalive falhou (normal):', error);
      }
    }, 30000); // A cada 30 segundos
  }

  /**
   * Para o keepalive manual
   */
  private stopKeepalive(): void {
    if (this.keepaliveInterval) {
      clearInterval(this.keepaliveInterval);
      this.keepaliveInterval = null;
      Logger.log('Parando keepalive manual para Mikrotik');
    }
  }

  /**
   * Limpa todos os recursos
   */
  private cleanup(): void {
    this.stopKeepalive();
    this.closeApi();
  }

  /**
   * Fecha a conexão com a API do RouterOS
   */
  public async closeApi(): Promise<void> {
    if (this.routerOsApi && this.apiConnected) {
      try {
        Logger.log(`Fechando conexão API Mikrotik para ${this.host}`);
        await this.routerOsApi.close();
        this.apiConnected = false;
      } catch (error) {
        Logger.error('Erro ao fechar conexão API Mikrotik:', error);
      }
    }
    this.routerOsApi = null;
  }

  /**
   * Inicializa a API do RouterOS (desabilitada por padrão)
   */
  private initializeApi(): void {
    // API desabilitada por padrão para priorizar SSH
    // Pode ser habilitada se necessário
    Logger.log('API Mikrotik desabilitada, usando apenas SSH');
    return;

    /*
    try {
      this.routerOsApi = new RouterOSAPI({
        host: this.host,
        user: this.username,
        password: this.password,
        port: this.port,
        keepalive: true,
        timeout: 20000
      });

      Logger.log(`API Mikrotik inicializada para ${this.host}`);
    } catch (error) {
      Logger.error('Erro ao inicializar API Mikrotik:', error);
      this.routerOsApi = null;
    }
    */
  }

  /**
   * Conecta à API do RouterOS (desabilitada por padrão)
   */
  private async connectApi(): Promise<boolean> {
    // API desabilitada por padrão para priorizar SSH
    return false;

    /*
    if (!this.routerOsApi) {
      this.initializeApi();
      if (!this.routerOsApi) {
        return false;
      }
    }

    try {
      if (this.apiConnected) {
        // Verificar se a conexão ainda está ativa
        try {
          // Teste simples para verificar se a conexão está ativa
          await this.routerOsApi.write('/system/identity/print');
          return true;
        } catch (error) {
          Logger.log('Conexão API Mikrotik perdida, reconectando...');
          this.apiConnected = false;
        }
      }

      this.connectionAttempts++;
      Logger.log(`Conectando à API Mikrotik (${this.host}) - Tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts}`);

      // Criar uma promise com timeout de 3 segundos
      const connectPromise = this.routerOsApi.connect();
      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(() => {
          reject(new Error('API_TIMEOUT'));
        }, 3000); // 3 segundos
      });

      // Executar com timeout
      await Promise.race([connectPromise, timeoutPromise]);

      this.apiConnected = true;
      this.connectionAttempts = 0;

      Logger.log(`Conexão API Mikrotik estabelecida com sucesso: ${this.host}`);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage === 'API_TIMEOUT') {
        Logger.error(`Timeout ao conectar à API Mikrotik (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
      } else {
        Logger.error(`Falha ao conectar à API Mikrotik (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts}):`, error);
      }

      if (this.connectionAttempts >= this.maxConnectionAttempts) {
        this.connectionAttempts = 0;
        Logger.log('Número máximo de tentativas de conexão API Mikrotik excedido, usando SSH como fallback');
        return false;
      }

      return false;
    }
    */
  }

  /**
   * Verifica se um comando é interativo e precisa de PTY
   * @param command Comando a ser verificado
   * @returns true se o comando é interativo
   */
  private isInteractiveCommand(command: string): boolean {
    const interactiveCommands = [
      'ping',
      'traceroute',
      'tool ping',
      'tool traceroute',
      'tool bandwidth-test',
      'tool sniffer',
      'tool torch',
      'tool netwatch',
      'tool speed-test'
    ];

    const cleanCommand = command.toLowerCase().trim();
    return interactiveCommands.some(cmd => cleanCommand.includes(cmd));
  }

  /**
   * Formata comandos de diagnóstico para a sintaxe correta do Mikrotik
   * @param command Comando original
   * @returns Comando formatado
   */
  private formatDiagnosticCommand(command: string): string {
    const cleanCommand = command.trim().toLowerCase();

    // Detectar comandos ping simples e converter para sintaxe Mikrotik
    if (cleanCommand.startsWith('ping ') || cleanCommand === 'ping') {
      const parts = command.trim().split(/\s+/);
      if (parts.length >= 2) {
        const target = parts[1];
        // Usar sintaxe /ping com parâmetros padrão
        return `/ping address=${target} count=4`;
      }
      return '/ping';
    }

    // Detectar comandos traceroute simples e converter para sintaxe Mikrotik
    if (cleanCommand.startsWith('traceroute ') || cleanCommand === 'traceroute') {
      const parts = command.trim().split(/\s+/);
      if (parts.length >= 2) {
        const target = parts[1];
        // Usar sintaxe /tool traceroute
        return `/tool traceroute address=${target}`;
      }
      return '/tool traceroute';
    }

    // Para comandos que já começam com /, manter como estão
    if (command.startsWith('/')) {
      return command;
    }

    // Adicionar / no início para outros comandos
    return `/${command}`;
  }

  /**
   * Executa um comando em um dispositivo Mikrotik
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Atualizar timestamp do último comando
      this.lastCommandTime = Date.now();

      Logger.log('Executando comando Mikrotik:', command);

      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando multilinhas para Mikrotik');
        return await this.executeMultilineCommand(command);
      }

      // Verificar se é um comando interativo
      const isInteractive = this.isInteractiveCommand(command);
      Logger.log(`Comando ${isInteractive ? 'interativo' : 'não-interativo'} detectado: ${command}`);

      if (isInteractive) {
        return await this.executeInteractiveCommand(command);
      }

      // Formatar o comando usando a função específica para diagnósticos
      const formattedCommand = this.formatDiagnosticCommand(command);

      Logger.log(`Executando comando Mikrotik formatado: ${formattedCommand}`);

      // Usar apenas SSH (API desabilitada)
      Logger.log('Usando SSH para executar comando Mikrotik');

      // Calcular o comando sem a barra inicial para SSH
      const cmd = formattedCommand.startsWith('/') ? formattedCommand.slice(1) : formattedCommand;

      // Configurar opções SSH otimizadas para Mikrotik
      const sshOptions = {
        execOptions: {
          pty: false,  // Desabilitar PTY para melhor estabilidade
          env: {}      // Ambiente limpo
        },
        timeout: this.calculateDynamicTimeout(1), // Timeout dinâmico para comando único
        onStdout: (chunk: any) => {
          // Log simplificado para reduzir overhead
          if (chunk.length > 0) {
            Logger.log(`stdout Mikrotik: ${chunk.length} bytes recebidos`);
          }
        },
        onStderr: (chunk: any) => {
          // Log simplificado para reduzir overhead
          if (chunk.length > 0) {
            Logger.error(`stderr Mikrotik: ${chunk.length} bytes recebidos`);
          }
        }
      };

      // Executar o comando via SSH com retry automático
      let result;
      let retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          Logger.log(`Tentativa ${retryCount + 1}/${maxRetries + 1} de execução do comando`);

          result = await this.ssh.execCommand(cmd, sshOptions);

          // Se chegou aqui, comando executado com sucesso
          break;
        } catch (error) {
          retryCount++;
          const errorMessage = error instanceof Error ? error.message : String(error);

          Logger.error(`Erro na tentativa ${retryCount}/${maxRetries + 1}:`, errorMessage);

          // Verificar se é um erro de timeout/keepalive
          if (errorMessage.includes('keepalive') ||
              errorMessage.includes('timeout') ||
              errorMessage.includes('timed out') ||
              errorMessage.includes('connection') ||
              errorMessage.includes('closed')) {

            if (retryCount <= maxRetries) {
              Logger.log(`Aguardando 2 segundos antes da próxima tentativa...`);
              await new Promise(resolve => setTimeout(resolve, 2000));
              continue;
            } else {
              Logger.error('Máximo de tentativas excedido, sinalizando necessidade de reconexão');
              throw new Error('RECONNECT_NEEDED: Falha persistente de conexão SSH');
            }
          } else {
            // Erro não relacionado a conexão, não tentar novamente
            throw error;
          }
        }
      }

      if (!result) {
        throw new Error('Falha ao executar comando após todas as tentativas');
      }

      // Limpar a saída final
      const cleanStdout = this.cleanAnsiOutput(result.stdout);
      const cleanStderr = this.cleanAnsiOutput(result.stderr);

      // Formatar a saída para melhor legibilidade
      const formattedOutput = this.formatMikrotikOutput(cleanStdout);

      Logger.log(`Comando Mikrotik executado com sucesso. Saída: ${formattedOutput.length} caracteres`);

      return {
        stdout: formattedOutput,
        stderr: cleanStderr,
        code: result.code || 0
      };
    } catch (error) {
      Logger.error('Erro ao executar comando Mikrotik:', error);

      // Verificar se é um erro de keepalive timeout
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('RECONNECT_NEEDED') ||
          errorMessage.includes('keepalive') ||
          errorMessage.includes('timeout') ||
          errorMessage.includes('timed out') ||
          errorMessage.includes('connection') ||
          errorMessage.includes('closed')) {
        Logger.log('Detectado erro de conexão, sinalizando necessidade de reconexão');
        throw new Error('RECONNECT_NEEDED: Erro de conexão SSH detectado');
      }

      throw error;
    }
  }

  /**
   * Executa um comando com múltiplas linhas
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  async executeMultilineCommand(command: string): Promise<CommandResult> {
    try {
      // Atualizar timestamp do último comando
      this.lastCommandTime = Date.now();

      // Dividir o comando em linhas individuais
      const lines = command.split('\n').filter(line => line.trim() !== '');
      const commandCount = lines.length;

      // Calcular o timeout dinâmico com base na quantidade de comandos
      const dynamicTimeout = this.calculateDynamicTimeout(commandCount);

      Logger.log(`Executando ${commandCount} comandos Mikrotik separados com timeout dinâmico de ${dynamicTimeout}ms`);

      // Usar apenas SSH (API desabilitada)
      Logger.log('Usando SSH para executar comandos múltiplos Mikrotik');

      // Criar uma promise com timeout global para todos os comandos
      const timeoutPromise = new Promise<CommandResult>((resolve) => {
        setTimeout(() => {
          resolve({
            stdout: '',
            stderr: `[ERRO] Timeout global atingido após ${dynamicTimeout/1000} segundos para ${commandCount} comandos`,
            code: 124 // Código de timeout
          });
        }, dynamicTimeout);
      });

      // Promise para executar todos os comandos
      const executePromise = async (): Promise<CommandResult> => {
        let combinedOutput = '';
        let combinedError = '';
        let lastCode = 0;
        let successfulCommands = 0;

        // Executar cada linha separadamente
        for (let i = 0; i < commandCount; i++) {
          const line = lines[i].trim();
          Logger.log(`Executando linha ${i+1}/${commandCount}: ${line}`);

          // Formatar o comando para SSH
          const cleanCommand = line.replace(/\s+/g, ' ');
          const formattedCommand = cleanCommand.startsWith('/') ? cleanCommand : `/${cleanCommand}`;

          try {
            // Retira a barra inicial
            const cmd = formattedCommand.startsWith('/')
              ? formattedCommand.slice(1)
              : formattedCommand;

            // Configurar opções SSH otimizadas
            const sshOptions = {
              execOptions: {
                pty: false,  // Desabilitar PTY para melhor estabilidade
                env: {}      // Ambiente limpo
              },
              timeout: Math.min(this.calculateDynamicTimeout(1), 30000), // Timeout por comando individual
              onStdout: (chunk: any) => {
                if (chunk.length > 0) {
                  Logger.log(`stdout linha ${i+1}/${commandCount}: ${chunk.length} bytes`);
                }
              },
              onStderr: (chunk: any) => {
                if (chunk.length > 0) {
                  Logger.error(`stderr linha ${i+1}/${commandCount}: ${chunk.length} bytes`);
                }
              }
            };

            // Executar comando com retry limitado
            let result;
            let retryCount = 0;
            const maxRetries = 1; // Apenas 1 retry para comandos múltiplos

            while (retryCount <= maxRetries) {
              try {
                result = await this.ssh.execCommand(cmd, sshOptions);
                break; // Sucesso, sair do loop de retry
              } catch (error) {
                retryCount++;
                const errorMessage = error instanceof Error ? error.message : String(error);

                if (retryCount <= maxRetries &&
                    (errorMessage.includes('timeout') || errorMessage.includes('connection'))) {
                  Logger.log(`Retry ${retryCount}/${maxRetries} para linha ${i+1}`);
                  await new Promise(resolve => setTimeout(resolve, 1000));
                  continue;
                } else {
                  throw error;
                }
              }
            }

            if (result) {
              // Limpar e formatar a saída
              const cleanStdout = this.cleanAnsiOutput(result.stdout);
              const formattedStdout = this.formatMikrotikOutput(cleanStdout);
              combinedOutput += `=== Comando: ${formattedCommand} ===\n${formattedStdout}\n\n`;

              if (result.stderr) {
                const cleanStderr = this.cleanAnsiOutput(result.stderr);
                combinedError += `=== Aviso em: ${formattedCommand} ===\n${cleanStderr}\n\n`;
              }

              lastCode = result.code || 0;
              successfulCommands++;
            }
          } catch (error) {
            Logger.error(`Erro ao executar linha ${i+1}/${commandCount}:`, error);
            const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
            combinedError += `=== Erro em: ${formattedCommand} ===\n${errorMessage}\n\n`;
            lastCode = 1;

            // Verificar se é erro crítico de conexão
            if (errorMessage.includes('keepalive') ||
                errorMessage.includes('connection') ||
                errorMessage.includes('closed')) {
              Logger.error('Erro crítico de conexão detectado, interrompendo execução múltipla');
              combinedError += `\n[ERRO CRÍTICO] Conexão SSH perdida. Comandos restantes cancelados.\n`;
              break;
            }
          }

          // Pequena pausa entre comandos para evitar sobrecarga
          if (i < commandCount - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        Logger.log(`Execução múltipla concluída: ${successfulCommands}/${commandCount} comandos executados com sucesso`);

        return {
          stdout: combinedOutput,
          stderr: combinedError,
          code: lastCode
        };
      };

      // Executar com timeout
      return Promise.race([executePromise(), timeoutPromise]);
    } catch (error) {
      Logger.error('Erro ao executar comandos múltiplos Mikrotik:', error);

      // Verificar se é erro de conexão
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('keepalive') ||
          errorMessage.includes('timeout') ||
          errorMessage.includes('connection') ||
          errorMessage.includes('closed')) {
        throw new Error('RECONNECT_NEEDED: Erro de conexão durante execução múltipla');
      }

      throw error;
    }
  }

  /**
   * Limpa caracteres de controle ANSI da saída do terminal
   * @param output Saída do terminal
   * @returns Saída limpa
   */
  private cleanAnsiOutput(output: string): string {
    try {
      if (!output) return '';

      // Remover códigos de cores ANSI e outros caracteres de controle
      const ansiRegex = /\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])/g;
      let cleanOutput = output.replace(ansiRegex, '');

      // Remover caracteres de controle adicionais específicos do Mikrotik
      cleanOutput = cleanOutput.replace(/\[m/g, '');
      cleanOutput = cleanOutput.replace(/\[K/g, '');
      cleanOutput = cleanOutput.replace(/\[9999B/g, '');
      cleanOutput = cleanOutput.replace(/\[9999D/g, '');
      cleanOutput = cleanOutput.replace(/\[9999C/g, '');

      // Remover sequências específicas do Mikrotik
      cleanOutput = cleanOutput.replace(/\[m\s*\[m/g, '');
      cleanOutput = cleanOutput.replace(/\[1m\s*\[m/g, '');

      // Remover caracteres não imprimíveis, mas preservar quebras de linha
      cleanOutput = cleanOutput.replace(/[\x00-\x08\x0B-\x0C\x0E-\x1F\x7F-\x9F]/g, '');

      // Tratar caracteres específicos do Mikrotik
      cleanOutput = cleanOutput.replace(/\[[\d;]*[a-zA-Z]/g, '');

      // Remover caracteres de controle específicos de comandos interativos
      cleanOutput = cleanOutput.replace(/\r\n/g, '\n'); // Normalizar quebras de linha
      cleanOutput = cleanOutput.replace(/\r/g, '\n'); // Converter CR para LF

      // Processar linha por linha para melhor formatação
      const lines = cleanOutput.split('\n');
      const processedLines = lines.map(line => {
        // Remover espaços extras e caracteres de controle residuais
        let processedLine = line.trim()
          .replace(/\s+/g, ' ')
          .replace(/\[\d+[A-Z]/g, '')
          .replace(/\[\d+;\d+[A-Z]/g, '');

        return processedLine;
      }).filter(line => line.length > 0); // Remover linhas vazias

      // Juntar as linhas novamente
      cleanOutput = processedLines.join('\n');

      // Remover linhas vazias duplicadas
      cleanOutput = cleanOutput.replace(/\n\s*\n/g, '\n');

      return cleanOutput;
    } catch (error) {
      Logger.error('Erro ao limpar saída ANSI:', error);
      return output; // Retornar a saída original em caso de erro
    }
  }

  /**
   * Formata a saída do Mikrotik para melhor legibilidade
   * @param output Saída limpa do Mikrotik
   * @returns Saída formatada
   */
  private formatMikrotikOutput(output: string): string {
    try {
      if (!output) return '';

      // Dividir em linhas para processamento
      const lines = output.split('\n');

      // Detectar se é uma tabela simples
      const isTable = lines.some(line =>
        line.includes('NAME') &&
        (line.includes('TYPE') || line.includes('MTU') || line.includes('INTERFACE'))
      );

      if (isTable) {
        // Formatar como tabela simples
        return this.formatMikrotikTable(lines);
      }

      // Formatar saída padrão - apenas limpar e organizar
      const formattedLines = lines
        .map(line => line.trim())
        .filter(line => line.length > 0); // Remover linhas vazias

      return formattedLines.join('\n');
    } catch (error) {
      Logger.error('Erro ao formatar saída Mikrotik:', error);
      return output; // Retornar a saída original em caso de erro
    }
  }

  /**
   * Formata uma tabela do Mikrotik de forma simples
   * @param lines Linhas da tabela
   * @returns Tabela formatada
   */
  private formatMikrotikTable(lines: string[]): string {
    try {
      // Encontrar a linha de cabeçalho
      const headerIndex = lines.findIndex(line =>
        line.includes('NAME') &&
        (line.includes('TYPE') || line.includes('MTU') || line.includes('INTERFACE'))
      );

      if (headerIndex === -1) {
        // Não é uma tabela, retornar as linhas originais
        return lines.filter(line => line.trim().length > 0).join('\n');
      }

      // Formatar de forma simples - apenas organizar as linhas
      let formattedOutput = '';

      // Adicionar todas as linhas a partir do cabeçalho
      for (let i = headerIndex; i < lines.length; i++) {
        const line = lines[i].trim();

        // Pular linhas vazias
        if (!line) continue;

        // Adicionar linha com formatação mínima
        formattedOutput += line + '\n';
      }

      return formattedOutput;
    } catch (error) {
      Logger.error('Erro ao formatar tabela Mikrotik:', error);
      return lines.filter(line => line.trim().length > 0).join('\n');
    }
  }

  /**
   * Executa um comando interativo em um dispositivo Mikrotik usando shell
   * @param command Comando interativo a ser executado
   * @returns Resultado do comando
   */
  async executeInteractiveCommand(command: string): Promise<CommandResult> {
    return new Promise(async (resolve, reject) => {
      let shell: any = null;
      let output = '';
      let errorOutput = '';
      let commandCompleted = false;
      let timeoutId: NodeJS.Timeout | null = null;

      try {
        // Atualizar timestamp do último comando
        this.lastCommandTime = Date.now();

        Logger.log(`Executando comando interativo Mikrotik: ${command}`);

        // Formatar o comando usando a função específica para diagnósticos
        const formattedCommand = this.formatDiagnosticCommand(command);

        // Timeout específico para comandos interativos (mais longo)
        const interactiveTimeout = 90000; // 90 segundos para comandos interativos

        // Configurar timeout global
        timeoutId = setTimeout(() => {
          if (!commandCompleted) {
            Logger.log('Timeout atingido para comando interativo Mikrotik');
            commandCompleted = true;
            if (shell) {
              try {
                shell.end();
              } catch (e) {
                // Ignorar erros ao fechar shell
              }
            }
            resolve({
              stdout: output || '[TIMEOUT] Comando interativo não completou dentro do tempo limite',
              stderr: errorOutput,
              code: 124 // Código de timeout
            });
          }
        }, interactiveTimeout);

        // Iniciar shell interativo com PTY habilitado
        shell = await this.ssh.requestShell({
          term: 'vt100',
          rows: 24,
          cols: 120,
          wrap: 120,
          ptyType: 'vanilla'
        });

        // Configurar handlers para eventos
        shell.on('data', (data: Buffer) => {
          if (commandCompleted) return;

          const chunk = data.toString('utf8');
          output += chunk;

          Logger.log(`Recebido comando interativo (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);

          // Verificar se detectamos o prompt "More:" para continuar
          if (chunk.includes('More:') || chunk.includes('[Q quit|SPACE continue|ENTER line]')) {
            shell.write(' ');
            Logger.log('Detectado prompt de paginação, enviando espaço para continuar');
            return;
          }

          // Detectar conclusão de comandos PING
          if (command.toLowerCase().includes('ping')) {
            // Padrões que indicam conclusão do ping
            if ((chunk.includes('sent=') && chunk.includes('received=')) ||
                chunk.includes('ping statistics') ||
                chunk.includes('packets transmitted') ||
                chunk.includes('interrupted') ||
                chunk.includes('timeout') ||
                chunk.includes('host unreachable') ||
                chunk.includes('no route to host')) {
              Logger.log('Comando PING completado - padrão de conclusão detectado');
              commandCompleted = true;
              if (timeoutId) clearTimeout(timeoutId);
              shell.end();
              resolve({
                stdout: this.cleanAnsiOutput(output),
                stderr: this.cleanAnsiOutput(errorOutput),
                code: 0
              });
              return;
            }
          }

          // Detectar conclusão de comandos TRACEROUTE
          if (command.toLowerCase().includes('traceroute')) {
            // Padrões que indicam conclusão do traceroute
            if (chunk.includes('trace complete') ||
                chunk.includes('traceroute to') && chunk.includes('hops max') ||
                chunk.includes('destination reached') ||
                chunk.includes('no route to host') ||
                chunk.includes('timeout') ||
                chunk.includes('interrupted')) {
              Logger.log('Comando TRACEROUTE completado - padrão de conclusão detectado');
              commandCompleted = true;
              if (timeoutId) clearTimeout(timeoutId);
              shell.end();
              resolve({
                stdout: this.cleanAnsiOutput(output),
                stderr: this.cleanAnsiOutput(errorOutput),
                code: 0
              });
              return;
            }
          }

          // Detectar prompt do Mikrotik para outros comandos interativos
          if ((chunk.includes('[admin@') && chunk.includes('> ')) ||
              chunk.includes('RouterOS') && chunk.includes('> ') ||
              chunk.includes('MikroTik') && chunk.includes('> ')) {
            Logger.log('Prompt do Mikrotik detectado, comando interativo completado');
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            shell.end();
            resolve({
              stdout: this.cleanAnsiOutput(output),
              stderr: this.cleanAnsiOutput(errorOutput),
              code: 0
            });
            return;
          }
        });

        shell.stderr?.on('data', (data: Buffer) => {
          if (commandCompleted) return;
          const chunk = data.toString('utf8');
          errorOutput += chunk;
          Logger.error(`stderr comando interativo: ${chunk}`);
        });

        shell.on('close', () => {
          if (!commandCompleted) {
            Logger.log('Shell fechado para comando interativo');
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            resolve({
              stdout: this.cleanAnsiOutput(output),
              stderr: this.cleanAnsiOutput(errorOutput),
              code: 0
            });
          }
        });

        shell.on('error', (error: Error) => {
          if (!commandCompleted) {
            Logger.error('Erro no shell interativo:', error);
            commandCompleted = true;
            if (timeoutId) clearTimeout(timeoutId);
            reject(error);
          }
        });

        // Aguardar um pouco para o shell estar pronto
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Enviar o comando
        Logger.log(`Enviando comando interativo: ${formattedCommand}`);
        shell.write(formattedCommand + '\r\n');

        // Configurar timeouts específicos para diferentes tipos de comando
        if (command.toLowerCase().includes('ping')) {
          // Para PING, aguardar mais tempo antes de interromper (30 segundos)
          setTimeout(() => {
            if (!commandCompleted) {
              Logger.log('Enviando Ctrl+C para interromper PING após timeout');
              shell.write('\x03'); // Ctrl+C
            }
          }, 30000); // Aguardar 30 segundos antes de interromper
        } else if (command.toLowerCase().includes('traceroute')) {
          // Para TRACEROUTE, aguardar ainda mais tempo (45 segundos)
          setTimeout(() => {
            if (!commandCompleted) {
              Logger.log('Enviando Ctrl+C para interromper TRACEROUTE após timeout');
              shell.write('\x03'); // Ctrl+C
            }
          }, 45000); // Aguardar 45 segundos antes de interromper
        }

      } catch (error) {
        Logger.error('Erro ao executar comando interativo Mikrotik:', error);
        commandCompleted = true;
        if (timeoutId) clearTimeout(timeoutId);
        if (shell) {
          try {
            shell.end();
          } catch (e) {
            // Ignorar erros ao fechar shell
          }
        }
        reject(error);
      }
    });
  }
}
